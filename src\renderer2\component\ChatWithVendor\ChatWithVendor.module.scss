.container {

    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .chatHeader {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        .poNumberMain {
            font-family: Syncopate;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            span {
                background-image: linear-gradient(to right, #9b9eac, #fff);
                font-family: Syncopate;
                font-size: 18px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: normal;
                text-align: left;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-left: 4px;
            }
        }

        .vendorName {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #fff;

            .vendorRole {
                font-size: 10px;
                font-weight: 300;
                display: block;
                margin-top: 8px;
            }
        }

        .btnCloseContainer{
            display: inline-flex;
            align-items: baseline;
            column-gap: 8px;
        }

    }

    .chatBody {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .chatMessages{
            width: 100%;
        }

        .messageContainer {
            display: flex;
            height: 248px;
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            overflow-y: auto;
            border-radius: 10.3px;
            background-color: rgba(217, 217, 217, 0.04);
            padding: 12px 8px 16px 8px;


            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }

            &::-webkit-scrollbar-thumb {
                background:
                    #9da2b2;
                border-radius: 50px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }

            .message {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-end;
                /* This makes the messages stack from the bottom */
                width: 100%;
                height: auto;
                margin-bottom: 12px;

                &.myMessage{
                    align-items: flex-end;
                }

                &.othersMessage{
                     .messageBubble {
                        background-color: #16b9ff;
                        color: #000;
                    }
                }

                .messageTimestamp {
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: 300;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.2;
                    letter-spacing: normal;
                    text-align: center;
                    color: #fff;
                    margin-bottom: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .messageBubble {
                    padding: 8px 8px 8px 8px;
                    border-radius: 6px;
                    background-color: #000;
                    color: #ffffff;
                    max-width: 70%;
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 1.24;
                    letter-spacing: normal;
                    text-align: left;
                    color: #fff;
                    word-wrap: break-word;
                    min-width: 30px;
                }
            }
        }
    }

    .inputSection {

        .formattingToolbarContainer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin-top: 6px;

            .formattingToolbar {
                display: flex;
                align-items: center;
                column-gap: 2px;

                .emojiContainer {
                    display: flex;
                    align-items: center;

                    .emojiPicker {
                        position: absolute;
                        bottom: 55px;
                        left: 12px;
                        width: 281px;
                        max-height: 70px;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 4px;
                        overflow-y: auto;
                        z-index: 10;
                        background-color: rgba(255, 255, 255, 0.5);
                        padding-top: 3px;
                        border-radius: 10.3px;

                        &::-webkit-scrollbar {
                            width: 5px;
                            height: 6px;
                        }

                        &::-webkit-scrollbar-thumb {
                            background: rgba(255, 255, 255, 0.32);
                            border-radius: 50px;
                        }

                        &::-webkit-scrollbar-track {
                            background: transparent;
                        }


                        &:before {
                            content: '';
                            position: absolute;
                            bottom: -8px;
                            left: 10px;
                            width: 0;
                            height: 0;
                            border-left: 8px solid transparent;
                            border-right: 8px solid transparent;
                            border-top: 8px solid rgba(0, 0, 0, 0.8);
                        }

                        .emojiOption {
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            font-size: 18px;
                            padding: 0;
                            margin: 0;
                            background: transparent;
                            border: none;
                            border-radius: 4px;
                            transition: background-color 0.2s;

                            &:hover {
                                background-color: rgba(255, 255, 255, 0.1);
                            }
                        }
                    }
                }

                .formattingButton {
                    background: transparent;
                    border: none;
                    color: #fff;
                    font-size: 18px;

                    .Icon {
                        width: 20px;
                        height: 20px;
                        display: block;
                    }

                    .IconHover {
                        width: 20px;
                        height: 20px;
                        display: none;
                    }

                    &:hover {
                        .Icon {
                            display: none;
                        }

                        .IconHover {
                            display: block;
                        }
                    }
                }

                .buttonClicked {
                    .Icon {
                        display: none;
                    }

                    .IconHover {
                        display: block;
                    }
                }

                .attachmentIcon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                }

                .emojiButton {
                    .Icon {
                        display: none;
                    }

                    .IconHover {
                        display: block;
                    }
                }
            }
        }

        .inputContainer {
            width: 100%;
            height: 64px;
            margin: 12px 0 0;
            border-radius: 12px;
            box-shadow: inset 4px 5px 2.2px 0 #000;
            background: url(../../assets/New-images/Share-Pricing/Email.svg) no-repeat;
            background-origin: border-box;
            background-clip: content-box, border-box;
            background-size: cover;
            background-position: bottom;

            .messageInput {
                width: 100%;
                height: 100%;
                padding: 14px 11px 12px 14.5px;
                border-radius: 12px;
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                border: 0px;
                background: transparent;
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: normal;
                color: rgba(255, 255, 255);
                text-align: left;

                &:focus {
                    outline: none;
                }

                // Placeholder styling for contentEditable
                &:empty::before {
                    content: attr(data-placeholder);
                    color: rgba(255, 255, 255, 0.4);
                    pointer-events: none;
                    position: absolute;
                }

            }
        }

        .sendButtonDisabled {
            width: 65px;
            height: 16px;
            gap: 8px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Syncopate;
            font-size: 9.4px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: -0.4px;
            text-align: center;
            color: rgba(255, 255, 255, 0.4);
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: not-allowed;
        }

        .sendButton {
            width: 65px;
            height: 16px;
            gap: 8px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.2);
            font-family: Syncopate;
            font-size: 9.4px;
            font-weight: bold;
            line-height: 1;
            letter-spacing: -0.4px;
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            
            &:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        }
    }
}
